package com.stationdm.workflow.serialization;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.stationdm.workflow.exception.KntException;
import com.stationdm.workflow.flow.base.ChoiceNode;
import com.stationdm.workflow.flow.base.WorkflowNode;
import com.stationdm.workflow.flow.container.ParallelContainer;
import com.stationdm.workflow.flow.container.SequenceContainer;
import com.stationdm.workflow.registry.NodeTypeRegistry;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

/**
 * WorkflowNode动态反序列化器
 * 基于注册中心动态创建节点实例，支持用户自定义节点类型
 */
@Component
@RequiredArgsConstructor
public class WorkflowNodeDeserializer extends JsonDeserializer<WorkflowNode> {

    private final NodeTypeRegistry registry;

    @Override
    public WorkflowNode deserialize(JsonParser p, DeserializationContext context) throws IOException {
        ObjectMapper mapper = (ObjectMapper) p.getCodec();
        JsonNode node = mapper.readTree(p);

        String type = node.get("type").asText();

        if (!registry.isSupported(type)) {
            throw new KntException("KNT_NODE_UNSUPPORTED_TYPE", "Unsupported node type: " + type);
        }

        Class<? extends WorkflowNode> nodeClass = registry.getNodeClass(type);
        return mapper.treeToValue(node, nodeClass);
    }
}