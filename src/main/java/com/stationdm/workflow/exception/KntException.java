package com.stationdm.workflow.exception;


import lombok.Getter;

public class KntException extends RuntimeException {
    @Getter
    private final String code;
    private final String message;

    public KntException(String code, String message) {
        super(message);
        this.code = code;
        this.message = message;
    }

    public KntException(String code, String message, Throwable cause) {
        super(message, cause);
        this.code = code;
        this.message = message;
    }

    @Override
    public String getMessage() {
        return this.message;
    }

}