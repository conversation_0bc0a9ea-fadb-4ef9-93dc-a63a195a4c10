
package com.stationdm.workflow.engine;

import cn.hutool.core.util.RandomUtil;
import com.stationdm.workflow.streaming.StreamingEvent;
import lombok.Data;
import org.springframework.context.ApplicationContext;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Sinks;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicLong;


@Data
public class WorkflowContext {

    private final Map<String, Object> data = new ConcurrentHashMap<>();
    private final ApplicationContext applicationContext;
    private final Sinks.Many<StreamingEvent> eventSink;
    private final String contextId;
    private final AtomicLong interactionCounter = new AtomicLong(0);
    private String sessionId;
    private final Map<String, Object> sessionData = new ConcurrentHashMap<>();

    public WorkflowContext(ApplicationContext applicationContext) {
        this.applicationContext = applicationContext;
        this.eventSink = Sinks.many().multicast().onBackpressureBuffer();
        this.contextId = RandomUtil.randomString(32).toLowerCase();
    }
    public void addNodeResult(String nodeId, Object result) {
        this.data.put(nodeId, result);
    }

    public Object getNodeResult(String nodeId) {
        return this.data.get(nodeId);
    }

    public <T> T getRequiredBean(Class<T> requiredType) {
        return this.applicationContext.getBean(requiredType);
    }

    public void emitStreamingEvent(String eventType, String nodeId, Object data) {
        StreamingEvent event = StreamingEvent.builder()
                .eventType(eventType)
                .nodeId(nodeId)
                .data(data)
                .build();
        eventSink.tryEmitNext(event);

    }
    public Flux<StreamingEvent> getEventStream() {
        return eventSink.asFlux();
    }

    public void completeEventStream() {
        eventSink.tryEmitComplete();
    }

    public void addSessionData(String sessionId, Object data) {
        this.sessionData.put(sessionId, data);
    }

    public <T> T getSessionData(String sessionId, Class<T> type) {
        Object value = this.sessionData.get(sessionId);
        if (value == null) {
            return null;
        }
        if (!type.isInstance(value)) {
            throw new ClassCastException("Stored object is not of type " + type.getName());
        }
        return type.cast(value);
    }

    /**
     * 生成唯一的交互ID
     * 格式: contextId_nodeId_interactionSequence
     */
    public String generateInteractionId(String nodeId) {
        long sequence = interactionCounter.incrementAndGet();
        return String.format("%s_%s_%d", contextId, nodeId, sequence);
    }
}