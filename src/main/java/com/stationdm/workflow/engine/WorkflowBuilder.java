
package com.stationdm.workflow.engine;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.stationdm.workflow.exception.KntException;
import com.stationdm.workflow.flow.Workflow;
import com.stationdm.workflow.flow.base.WorkflowNode;
import com.stationdm.workflow.flow.container.SequenceContainer;
import com.stationdm.workflow.flow.container.ParallelContainer;
import com.stationdm.workflow.flow.base.ChoiceNode;
import com.stationdm.workflow.registry.NodeTypeRegistry;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

@Component
@Slf4j
@RequiredArgsConstructor
public class WorkflowBuilder {

    private final ObjectMapper objectMapper;

    public SequenceContainer build(String workflowJson) throws JsonProcessingException {
        Workflow flow = objectMapper.readValue(workflowJson, Workflow.class);
        SequenceContainer rootSequence = new SequenceContainer();
        rootSequence.setId("root");
        rootSequence.setName("Root Sequence");
        rootSequence.setType("SEQUENCE");
        List<WorkflowNode> nodes = new ArrayList<>();
        for (JsonNode connection : flow.getConnections()) {
            nodes.add(parseNode(connection, flow.getNodes()));
        }
        rootSequence.setChildren(nodes);
        return rootSequence;
    }

    private WorkflowNode parseNode(JsonNode connection, List<WorkflowNode> availableNodes) {
        String id = connection.get("id").asText();
        String type = connection.get("type").asText();
        JsonNode children = connection.get("children");

        if ("SEQUENCE".equals(type) || "PARALLEL".equals(type)) {
            return createLogicalContainer(id, type, children, availableNodes);
        } else {
            return findAndConfigureConcreteNode(id, children, availableNodes);
        }
    }


    private WorkflowNode createLogicalContainer(String id, String type, JsonNode children, List<WorkflowNode> availableNodes) {
        WorkflowNode containerNode = "SEQUENCE".equals(type) ? new SequenceContainer() : new ParallelContainer();
        containerNode.setId(id);
        containerNode.setType(type);

        if (children != null && children.isArray()) {
            List<WorkflowNode> childNodes = parseChildren(children, availableNodes);
            if (containerNode instanceof SequenceContainer sc) {
                sc.setChildren(childNodes);
            } else {
                ParallelContainer pc = (ParallelContainer) containerNode;
                pc.setChildren(childNodes);
            }
        }
        return containerNode;
    }

    /**
     * 从可用节点列表中查找一个具象节点，并为其配置子节点（如果它是一个容器）。
     */
    private WorkflowNode findAndConfigureConcreteNode(String id, JsonNode children, List<WorkflowNode> availableNodes) {
        WorkflowNode node = findNodeById(id, availableNodes);
        if (node == null) {
            throw new KntException("KNT_WORKFLOW_BUILD_FAILED", "Node with id '" + id + "' is referenced in connections but not defined in the nodes list.");
        }
        // 如果这个具象节点恰好也是一个容器（如ChoiceNode），则为它设置子节点
        if (children != null && children.isArray() && node instanceof ChoiceNode choiceNode) {
            choiceNode.setChildren(parseChildren(children, availableNodes));
        }
        return node;
    }


    private List<WorkflowNode> parseChildren(JsonNode childrenNode, List<WorkflowNode> availableNodes) {
        List<WorkflowNode> children = new ArrayList<>();
        if (childrenNode != null && childrenNode.isArray()) {
            for (JsonNode child : childrenNode) {
                children.add(parseNode(child, availableNodes));
            }
        }
        return children;
    }

    private WorkflowNode findNodeById(String id, List<WorkflowNode> nodes) {
        if (nodes == null) {
            return null;
        }
        for (WorkflowNode node : nodes) {
            if (node.getId().equals(id)) {
                return node;
            }
        }
        return null;
    }
} 