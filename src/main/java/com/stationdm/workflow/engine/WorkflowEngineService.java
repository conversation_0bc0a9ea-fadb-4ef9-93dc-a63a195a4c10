package com.stationdm.workflow.engine;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.stationdm.workflow.flow.base.ChoiceNode;
import com.stationdm.workflow.flow.base.WorkflowNode;
import com.stationdm.workflow.flow.container.ParallelContainer;
import com.stationdm.workflow.flow.container.SequenceContainer;
import com.stationdm.workflow.flow.node.StartNode;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;

@Service
@Slf4j
@RequiredArgsConstructor
public class WorkflowEngineService {

    private final WorkflowBuilder workflowBuilder;
    private final ObjectMapper objectMapper;

    /**
     * 执行一个工作流。
     *
     * @param workflowJson 工作流的JSON定义
     * @param question     运行时的核心输入问题
     * @return 一个代表工作流执行完成的 Mono<Void>
     */
    public Mono<Void> execute(String workflowJson, String question, WorkflowContext context) throws JsonProcessingException {
        // 构建工作流的静态模板
        SequenceContainer rootSequence = workflowBuilder.build(workflowJson);

        // 准备初始数据，将运行时问题注入到StartNode中
        prepareInitialContext(rootSequence, question, context);

        // 开始执行
        return rootSequence.execute(context);
    }

    private void prepareInitialContext(SequenceContainer root, String question, WorkflowContext context) {
        Optional<StartNode> startNodeOpt = findStartNode(root);
        if (startNodeOpt.isPresent()) {
            StartNode startNode = startNodeOpt.get();
            JsonNode template = startNode.getInput();
            ArrayNode finalInput = objectMapper.createArrayNode();
            if (template != null && template.isArray()) {
                finalInput = template.deepCopy();
            }
            // 寻找一个约定好的name为"string"的变量并注入question
            boolean injected = false;
            for (JsonNode inputVar : finalInput) {
                if (inputVar.isObject() && inputVar.has("name") && "string".equals(inputVar.get("name").asText())) {
                    ((ObjectNode) inputVar).put("value", question);
                    injected = true;
                    break;
                }
            }
            if (!injected) {
                // 如果没有找到，创建一个默认的
                ObjectNode defaultInput = objectMapper.createObjectNode();
                defaultInput.put("name", "string");
                defaultInput.put("type", "string");
                defaultInput.put("value", question);
                finalInput.add(defaultInput);
            }
            context.addNodeResult(startNode.getId(), finalInput);
        }
    }

    private Optional<StartNode> findStartNode(WorkflowNode node) {
        if (node instanceof StartNode startNode) {
            return Optional.of(startNode);
        }

        List<WorkflowNode> children = null;
        if (node instanceof SequenceContainer sc) {
            children = sc.getChildren();
        } else if (node instanceof ParallelContainer pc) {
            children = pc.getChildren();
        } else if (node instanceof ChoiceNode cn) {
            children = cn.getChildren();
        }

        if (children != null) {
            for (WorkflowNode child : children) {
                Optional<StartNode> found = findStartNode(child);
                if (found.isPresent()) {
                    return found;
                }
            }
        }
        return Optional.empty();
    }
} 