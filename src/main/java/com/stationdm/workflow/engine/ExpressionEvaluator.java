
package com.stationdm.workflow.engine;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Component
@Slf4j
@RequiredArgsConstructor
public class ExpressionEvaluator {

    private static final Pattern EXPRESSION_PATTERN = Pattern.compile("\\{\\{\\s*\\$([\\w-]+)((?:\\.[\\w-]+)*)\\s*\\}\\}");

    private final ObjectMapper objectMapper;

    /**
     * 递归地对输入对象进行表达式求值。
     *
     * @param input   输入对象 (String, JsonNode, List, Map, etc.)
     * @param context 工作流上下文
     * @return 解析后的对象
     */
    public Object evaluate(Object input, WorkflowContext context) {
        if (input == null) {
            return null;
        }
        if (input instanceof String expression) {
            return evaluateString(expression, context);
        }
        if (input instanceof JsonNode jsonNode) {
            return evaluateJsonNode(jsonNode, context);
        }
        // 对于其他类型，暂不处理，直接返回
        return input;
    }

    private Object evaluateString(String expression, WorkflowContext context) {
        Matcher matcher = EXPRESSION_PATTERN.matcher(expression);
        if (matcher.matches()) {
            return evaluateSingleExpression(matcher, context);
        }
        StringBuffer resultBuffer = new StringBuffer();
        boolean found = false;
        while (matcher.find()) {
            found = true;
            Object value = evaluateSingleExpression(matcher, context);
            matcher.appendReplacement(resultBuffer, Matcher.quoteReplacement(String.valueOf(value)));
        }
        matcher.appendTail(resultBuffer);
        return found ? resultBuffer.toString() : expression;
    }

    private JsonNode evaluateJsonNode(JsonNode node, WorkflowContext context) {
        if (node.isObject()) {
            ObjectNode newNode = objectMapper.createObjectNode();
            node.fields().forEachRemaining(entry -> {
                // 递归地对子节点的值进行求值
                Object evaluatedValue = evaluate(entry.getValue(), context);
                // 将求值后的结果（可能是任何类型）转换回JsonNode并放入新对象中
                newNode.set(entry.getKey(), objectMapper.valueToTree(evaluatedValue));
            });
            return newNode;
        }
        if (node.isArray()) {
            ArrayNode newArray = objectMapper.createArrayNode();
            node.forEach(element -> {
                // 递归地对数组成员进行求值
                Object evaluatedValue = evaluate(element, context);
                newArray.add(objectMapper.valueToTree(evaluatedValue));
            });
            return newArray;
        }
        if (node.isTextual()) {
            // 如果JsonNode本身是一个文本节点，对其内容进行求值
            Object evaluated = evaluateString(node.asText(), context);
            return objectMapper.valueToTree(evaluated);
        }
        // 对于数字、布尔等原子类型，直接返回
        return node;
    }

    private Object evaluateSingleExpression(Matcher matcher, WorkflowContext context) {
        String nodeId = matcher.group(1);
        String path = matcher.group(2);

        Object nodeResult = context.getNodeResult(nodeId);
        if (nodeResult == null) {
            log.warn("Node result not found for ID: {}. Expression will be evaluated as null.", nodeId);
            return null;
        }

        if (path == null || path.isEmpty()) {
            return nodeResult;
        }

        JsonNode resultNode = objectMapper.valueToTree(nodeResult);
        String cleanPath = path.substring(1);
        JsonNode valueNode = getValueFromPath(resultNode, cleanPath);

        if (valueNode == null) {
            log.warn("Path not found: '{}' in result of node: {}. Expression will be evaluated as null.", cleanPath, nodeId);
            return null;
        }

        return convertJsonNodeToObject(valueNode);
    }

    /**
     * 根据节点结果的类型，智能地从中获取值。
     * - 如果节点结果是对象，则按点分路径查找。
     * - 如果节点结果是数组，则按变量名查找。
     */
    private JsonNode getValueFromPath(JsonNode resultNode, String path) {
        if (resultNode.isObject()) {
            // 标准对象：按点分路径遍历
            String[] parts = path.split("\\.");
            JsonNode currentNode = resultNode;
            for (String part : parts) {
                if (currentNode == null || !currentNode.isObject() || !currentNode.has(part)) {
                    return null;
                }
                currentNode = currentNode.get(part);
            }
            return currentNode;
        } else if (resultNode.isArray()) {
            // 变量数组：按名称查找
            // 在此模式下，我们假定 path 不包含点
            if (path.contains(".")) {
                log.warn("Dotted path is not supported for variable array results. Path: '{}'", path);
                return null;
            }
            for (JsonNode variableObject : resultNode) {
                if (variableObject.isObject() && variableObject.has("name") && path.equals(variableObject.get("name").asText())) {
                    return variableObject.get("value"); // 返回 "value" 字段
                }
            }
            return null; // 在数组中未找到
        } else {
            // 不支持的类型
            log.warn("Cannot perform path lookup on a non-container type: {}", resultNode.getNodeType());
            return null;
        }
    }

    private Object convertJsonNodeToObject(JsonNode node) {
        if (node.isMissingNode() || node.isNull()) return null;
        if (node.isBoolean()) return node.asBoolean();
        if (node.isInt()) return node.asInt();
        if (node.isLong()) return node.asLong();
        if (node.isDouble()) return node.asDouble();
        if (node.isTextual()) return node.asText();
        return node;
    }
} 