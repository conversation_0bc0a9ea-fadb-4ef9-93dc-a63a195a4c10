package com.stationdm.workflow.engine;

import com.stationdm.workflow.engine.annotation.Evaluate;
import com.stationdm.workflow.flow.base.WorkflowNode;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.ReflectionUtils;

/**
 * 节点输入处理器。
 * 在节点执行前，自动处理节点实例中需要进行表达式求值的输入字段。
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class NodeInputProcessor {

    private final ExpressionEvaluator expressionEvaluator;

    /**
     * 处理给定节点实例，解析所有被 @Evaluate 注解的字段。
     *
     * @param node    工作流节点实例
     * @param context 工作流上下文
     */
    public void process(WorkflowNode node, WorkflowContext context) {
        ReflectionUtils.doWithFields(node.getClass(), field -> {
            // 使私有字段可访问
            ReflectionUtils.makeAccessible(field);
            // 获取字段的当前值
            Object currentValue = ReflectionUtils.getField(field, node);
            if (currentValue != null) {
                Object newValue = expressionEvaluator.evaluate(currentValue, context);
                // 如果值发生了变化，则更新字段
                if (currentValue != newValue) {
                    ReflectionUtils.setField(field, node, newValue);
                    log.debug("Evaluated field '{}' of node '{}' from [{}] to [{}]",
                            field.getName(), node.getId(), currentValue, newValue);
                }
            }
        }, field -> field.isAnnotationPresent(Evaluate.class));
    }
} 