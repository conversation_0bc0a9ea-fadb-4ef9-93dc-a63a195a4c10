package com.stationdm.workflow.registry;

import com.stationdm.workflow.exception.KntException;
import com.stationdm.workflow.flow.base.WorkflowNode;
import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 节点类型注册中心
 * 支持动态注册自定义节点类型，基于Class反射创建节点实例
 */
@Component
@Slf4j
public class NodeTypeRegistry {
    private final Map<String, Class<? extends WorkflowNode>> nodeTypes = new ConcurrentHashMap<>();

    /**
     * 初始化注册内置节点
     */
    @PostConstruct
    public void init() {
        registerBuiltinNodes();
    }

    /**
     * 注册节点类型
     */
    public void registerNodeType(String type, Class<? extends WorkflowNode> nodeClass) {
        nodeTypes.put(type, nodeClass);
        log.debug("Registered node type: {} -> {}", type, nodeClass.getSimpleName());
    }

    /**
     * 根据类型创建节点实例
     */
    public WorkflowNode createNode(String type) {
        Class<? extends WorkflowNode> nodeClass = nodeTypes.get(type);
        if (nodeClass == null) {
            throw new KntException("KNT_NODE_UNSUPPORTED_TYPE", "Unsupported node type: " + type);
        }

        try {
            return nodeClass.getDeclaredConstructor().newInstance();
        } catch (Exception e) {
            throw new KntException("KNT_NODE_CREATE_FAILED", "Failed to create node instance for type: " + type, e);
        }
    }

    /**
     * 检查是否支持某个节点类型
     */
    public boolean isSupported(String type) {
        return nodeTypes.containsKey(type);
    }

    /**
     * 获取节点类型对应的Class
     */
    public Class<? extends WorkflowNode> getNodeClass(String type) {
        return nodeTypes.get(type);
    }

    private void registerBuiltinNodes() {
        try {
            // 注册内置容器节点
            registerNodeType("SEQUENCE",
                    Class.forName("com.stationdm.workflow.flow.container.SequenceContainer").asSubclass(WorkflowNode.class));
            registerNodeType("PARALLEL",
                    Class.forName("com.stationdm.workflow.flow.container.ParallelContainer").asSubclass(WorkflowNode.class));
            // 注册内置执行节点
            registerNodeType("START",
                    Class.forName("com.stationdm.workflow.flow.node.StartNode").asSubclass(WorkflowNode.class));
            registerNodeType("END",
                    Class.forName("com.stationdm.workflow.flow.node.EndNode").asSubclass(WorkflowNode.class));

        } catch (Exception e) {
            throw new KntException("KNT_NODE_REGISTRY_FAILED", "Failed to register builtin node types", e);
        }
    }
}