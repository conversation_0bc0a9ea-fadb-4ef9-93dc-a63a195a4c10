
package com.stationdm.workflow.config;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.module.SimpleModule;
import com.stationdm.workflow.flow.base.WorkflowNode;
import com.stationdm.workflow.serialization.WorkflowNodeDeserializer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class JacksonConfig {

    @Bean
    public ObjectMapper objectMapper(WorkflowNodeDeserializer workflowNodeDeserializer) {
        ObjectMapper mapper = new ObjectMapper();
        SimpleModule module = new SimpleModule();
        module.addDeserializer(WorkflowNode.class, workflowNodeDeserializer);
        mapper.registerModule(module);
        return mapper;
    }
} 