package com.stationdm.workflow.handler;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;
import reactor.core.publisher.Sinks;

import java.time.Duration;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;


@Slf4j
@Component
public class ChatHandler {

    private final Map<String, Sinks.One<String>> pendingInputs = new ConcurrentHashMap<>();

    public Mono<String> waitForUserInput(String interactionId, String question, int timeoutMinutes) {
        log.info("InteractionID [{}] is asking: {}", interactionId, question);
        Sinks.One<String> sink = Sinks.one();
        pendingInputs.put(interactionId, sink);
        return sink.asMono()
                .timeout(Duration.ofMinutes(timeoutMinutes))
                .doFinally(signal -> cleanup(interactionId));
    }

    public boolean submitUserInput(String interactionId, String userInput) {
        log.info("InteractionID [{}] received answer: {}", interactionId, userInput);
        Sinks.One<String> sink = pendingInputs.get(interactionId);
        if (sink != null) {
            Sinks.EmitResult result = sink.tryEmitValue(userInput);
            if (result.isSuccess()) {
                return true;
            } else {
                log.warn("Failed to emit value for InteractionID [{}]. Result: {}", interactionId, result);
                cleanup(interactionId);
                return false;
            }
        } else {
            log.warn("No pending input found for InteractionID [{}]", interactionId);
            return false;
        }
    }

    private void cleanup(String interactionId) {
        pendingInputs.remove(interactionId);
    }
}
