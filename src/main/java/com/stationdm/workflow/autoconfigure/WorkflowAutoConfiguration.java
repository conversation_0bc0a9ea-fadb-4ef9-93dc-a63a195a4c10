package com.stationdm.workflow.autoconfigure;

import com.stationdm.workflow.registry.NodeTypeRegistry;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.context.annotation.ComponentScan;


@AutoConfiguration
@ConditionalOnClass(NodeTypeRegistry.class)
@ComponentScan("com.stationdm.workflow")
public class WorkflowAutoConfiguration {

}
