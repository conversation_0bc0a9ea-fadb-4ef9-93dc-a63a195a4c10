package com.stationdm.workflow.flow.base;

import com.stationdm.workflow.engine.WorkflowContext;
import com.stationdm.workflow.handler.ChatHandler;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Mono;

/**
 * <AUTHOR>
 */
@Slf4j
@Getter
@Setter
@EqualsAndHashCode(callSuper = true)
public abstract class ChatNode extends WorkflowNode  {

    protected Mono<String> requestUserInput(String prompt, WorkflowContext context) {
        String interactionId = context.generateInteractionId(getId());
        context.emitStreamingEvent("REQUEST_USER_INPUT", interactionId, prompt);

        ChatHandler chatHandler = context.getRequiredBean(ChatHandler.class);
        return chatHandler.waitForUserInput(interactionId, prompt, 5);
    }
} 