package com.stationdm.workflow.flow.base;

import com.stationdm.workflow.engine.WorkflowContext;
import lombok.Data;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;


/**
 * <AUTHOR>
 */
@Data
public abstract class WorkflowNode implements WorkflowTask {
    private String id;
    private String name;
    private String type;

    protected void sendStreamingMessage(String message, WorkflowContext context) {
        context.emitStreamingEvent("NODE_MESSAGE", getId(), message);
    }

    protected Mono<Void> sendStreamingMessage(Flux<String> messageStream, WorkflowContext context) {
        return messageStream
                .doOnNext(chunk -> context.emitStreamingEvent("NODE_MESSAGE_CHUNK", getId(), chunk))
                .doOnComplete(() -> context.emitStreamingEvent("NODE_MESSAGE_END", getId(), "Stream completed"))
                .doOnError(error -> context.emitStreamingEvent("NODE_MESSAGE_ERROR", getId(), "Error in stream: " + error.getMessage()))
                .then();
    }
}