package com.stationdm.workflow.flow.base;

import com.stationdm.workflow.engine.NodeInputProcessor;
import com.stationdm.workflow.engine.WorkflowContext;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import reactor.core.publisher.Mono;

import java.util.List;

@Getter
@Setter
@EqualsAndHashCode(callSuper = true)
public abstract class ChoiceNode extends WorkflowNode {

    private List<WorkflowNode> children;

    @Override
    public final Mono<Void> execute(WorkflowContext context) {
        final var processor = context.getRequiredBean(NodeInputProcessor.class);
        return Mono.defer(() -> selectBranch(context))
                .map(branchIndex -> {
                    if (children == null || children.isEmpty()) {
                        return -1;
                    }
                    int max = children.size() - 1;
                    if (branchIndex < 0) return 0;
                    if (branchIndex > max) return max;
                    return branchIndex;
                })
                .flatMap(index -> {
                    if (index < 0) {
                        return Mono.empty();
                    }
                    WorkflowNode chosenChild = children.get(index);
                    // 在执行子节点前，先处理其输入，然后执行
                    return Mono.fromRunnable(() -> processor.process(chosenChild, context))
                            .then(chosenChild.execute(context));
                })
                .then();
    }

    /**
     * 选择要执行的分支索引
     * <p>
     * 此方法由子类实现，用于根据工作流上下文确定应该执行哪个子节点。
     * 返回的索引将用于从 children 列表中选择对应的子节点进行执行。
     * </p>
     * 
     * @param context 工作流执行上下文，包含当前执行状态和共享数据
     * @return 返回分支索引的Mono，有效范围为 0 到 children.size()-1。
     *         如果返回值超出范围，将被自动调整到有效范围内
     */
    protected abstract Mono<Integer> selectBranch(WorkflowContext context);
} 