
package com.stationdm.workflow.flow.base;

import com.stationdm.workflow.engine.WorkflowContext;
import reactor.core.publisher.Mono;

/**
 * 工作流任务节点的统一接口。
 * 任何一个可执行的节点（无论是原子节点还是容器节点）都必须实现此接口。
 */
@FunctionalInterface
public interface WorkflowTask {

    /**
     * 执行任务。
     *
     * @param context 工作流上下文，用于读取输入和写入输出
     * @return 一个 Mono<Void>，表示异步执行完成
     */
    Mono<Void> execute(WorkflowContext context);
} 