package com.stationdm.workflow.flow.container;

import com.stationdm.workflow.engine.NodeInputProcessor;
import com.stationdm.workflow.engine.WorkflowContext;
import com.stationdm.workflow.flow.base.WorkflowNode;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.util.ArrayList;
import java.util.List;

@Getter
@Setter
@EqualsAndHashCode(callSuper = true)
public class ParallelContainer extends WorkflowNode {
    private List<WorkflowNode> children;
    @Override
    public Mono<Void> execute(WorkflowContext context) {
        // 如果没有任务，直接返回空
        if (children == null || children.isEmpty()) {
            return Mono.empty();
        }

        // 从上下文中获取处理器
        var processor = context.getRequiredBean(NodeInputProcessor.class);

        // 将所有任务转换为 Mono<Void> 列表
        List<Mono<Void>> monoList = new ArrayList<>();
        for (WorkflowNode task : children) {
            // 注意：这里需要 Mono.fromRunnable 来确保 process 的调用发生在订阅时
            Mono<Void> processedTask = Mono.fromRunnable(() -> processor.process(task, context))
                                           .then(task.execute(context));
            monoList.add(processedTask);
        }
        // 使用 Flux.merge 并行执行所有任务
        return Flux.merge(monoList).then();
    }
}